import { RegisterUserDto } from 'src/dtos/user.dto';
import { User } from 'src/entities/user.entity';
import { AuthRepository } from 'src/repositories/auth.repository';
export declare class AuthController {
    private readonly authRepository;
    constructor(authRepository: AuthRepository);
    registerEmailAndPassword(body: RegisterUserDto): Promise<Partial<User>>;
    login(body: {
        email: string;
        password: string;
    }): Promise<{
        message: string;
        token: string;
    }>;
}
