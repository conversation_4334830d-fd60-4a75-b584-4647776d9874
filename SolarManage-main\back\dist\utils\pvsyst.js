"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.pvsystEnokoElSalto = exports.pvsystCentrovet601 = exports.pvsystCentrovet = exports.pvsystBS = void 0;
exports.pvsystBS = [
    { month: 1, year: 2023, estimatedGeneration: 27000 },
    { month: 2, year: 2023, estimatedGeneration: 20000 },
    { month: 3, year: 2023, estimatedGeneration: 29000 },
    { month: 4, year: 2023, estimatedGeneration: 17000 },
    { month: 5, year: 2023, estimatedGeneration: 12500 },
    { month: 6, year: 2023, estimatedGeneration: 9500 },
    { month: 7, year: 2023, estimatedGeneration: 17000 },
    { month: 8, year: 2023, estimatedGeneration: 20000 },
    { month: 9, year: 2023, estimatedGeneration: 28000 },
    { month: 10, year: 2023, estimatedGeneration: 34000 },
    { month: 11, year: 2023, estimatedGeneration: 42000 },
    { month: 12, year: 2023, estimatedGeneration: 56000 },
    { month: 1, year: 2024, estimatedGeneration: 31000 },
    { month: 2, year: 2024, estimatedGeneration: 18500 },
    { month: 3, year: 2024, estimatedGeneration: 24000 },
    { month: 4, year: 2024, estimatedGeneration: 18000 },
    { month: 5, year: 2024, estimatedGeneration: 16000 },
];
exports.pvsystCentrovet = [
    { month: 1, year: 2023, estimatedGeneration: 30000 },
    { month: 2, year: 2023, estimatedGeneration: 21000 },
    { month: 3, year: 2023, estimatedGeneration: 19000 },
    { month: 4, year: 2023, estimatedGeneration: 14000 },
    { month: 5, year: 2023, estimatedGeneration: 9000 },
    { month: 6, year: 2023, estimatedGeneration: 6000 },
    { month: 7, year: 2023, estimatedGeneration: 8200 },
    { month: 8, year: 2023, estimatedGeneration: 13000 },
    { month: 9, year: 2023, estimatedGeneration: 11500 },
    { month: 10, year: 2023, estimatedGeneration: 20000 },
    { month: 11, year: 2023, estimatedGeneration: 22000 },
    { month: 12, year: 2023, estimatedGeneration: 25000 },
    { month: 1, year: 2024, estimatedGeneration: 28500.2 },
    { month: 2, year: 2024, estimatedGeneration: 24352.7 },
    { month: 3, year: 2024, estimatedGeneration: 20135.1 },
    { month: 4, year: 2024, estimatedGeneration: 14654.6 },
    { month: 5, year: 2024, estimatedGeneration: 10137 },
];
exports.pvsystCentrovet601 = [
    { month: 1, year: 2023, estimatedGeneration: 11000 },
    { month: 2, year: 2023, estimatedGeneration: 10000 },
    { month: 3, year: 2023, estimatedGeneration: 12000 },
    { month: 4, year: 2023, estimatedGeneration: 7500 },
    { month: 5, year: 2023, estimatedGeneration: 6200 },
    { month: 6, year: 2023, estimatedGeneration: 4000 },
    { month: 7, year: 2023, estimatedGeneration: 5000 },
    { month: 8, year: 2023, estimatedGeneration: 6000 },
    { month: 9, year: 2023, estimatedGeneration: 7000 },
    { month: 10, year: 2023, estimatedGeneration: 8500 },
    { month: 11, year: 2023, estimatedGeneration: 12500 },
    { month: 12, year: 2023, estimatedGeneration: 13000 },
    { month: 1, year: 2024, estimatedGeneration: 15000 },
    { month: 2, year: 2024, estimatedGeneration: 12000 },
    { month: 3, year: 2024, estimatedGeneration: 13000 },
    { month: 4, year: 2024, estimatedGeneration: 6800 },
    { month: 5, year: 2024, estimatedGeneration: 5200 },
];
exports.pvsystEnokoElSalto = [
    { month: 1, year: 2023, estimatedGeneration: 3700 },
    { month: 2, year: 2023, estimatedGeneration: 2200 },
    { month: 3, year: 2023, estimatedGeneration: 2400 },
    { month: 4, year: 2023, estimatedGeneration: 1300 },
    { month: 5, year: 2023, estimatedGeneration: 1000 },
    { month: 6, year: 2023, estimatedGeneration: 650 },
    { month: 7, year: 2023, estimatedGeneration: 950 },
    { month: 8, year: 2023, estimatedGeneration: 1200 },
    { month: 9, year: 2023, estimatedGeneration: 1500 },
    { month: 10, year: 2023, estimatedGeneration: 1900 },
    { month: 11, year: 2023, estimatedGeneration: 2800 },
    { month: 12, year: 2023, estimatedGeneration: 2900 },
    { month: 1, year: 2024, estimatedGeneration: 3000 },
    { month: 2, year: 2024, estimatedGeneration: 2400 },
    { month: 3, year: 2024, estimatedGeneration: 2200 },
    { month: 4, year: 2024, estimatedGeneration: 1300 },
    { month: 5, year: 2024, estimatedGeneration: 1100 },
];
//# sourceMappingURL=pvsyst.js.map