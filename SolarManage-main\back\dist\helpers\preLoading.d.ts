import { Repository } from 'typeorm';
import { Panel } from 'src/entities/panel.entity';
import { pvsystPreloadRepository } from '../repositories/pvsystPreload.repository';
import { statsPreloadRepository } from '../repositories/statsPreload.repository';
import { AvailableYears } from 'src/entities/availableYears.entity';
import { Stats } from 'src/entities/stats.entity';
export declare class Preloading {
    private readonly panelRepository;
    private readonly availableYearsRepository;
    private readonly statsRepository;
    private readonly pvsystPreloadRepository;
    private readonly statsPreloadRepository;
    constructor(panelRepository: Repository<Panel>, availableYearsRepository: Repository<AvailableYears>, statsRepository: Repository<Stats>, pvsystPreloadRepository: pvsystPreloadRepository, statsPreloadRepository: statsPreloadRepository);
    preloading(): Promise<void>;
}
