"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const panel_entity_1 = require("../entities/panel.entity");
const stats_entity_1 = require("../entities/stats.entity");
const pvsyst_entity_1 = require("../entities/pvsyst.entity");
const typeorm_2 = require("@nestjs/typeorm");
const availableYears_entity_1 = require("../entities/availableYears.entity");
let DashboardService = class DashboardService {
    constructor(panelRepository, statsRepository, pvsystRepository, availableYearsRepository) {
        this.panelRepository = panelRepository;
        this.statsRepository = statsRepository;
        this.pvsystRepository = pvsystRepository;
        this.availableYearsRepository = availableYearsRepository;
    }
    async dataForDashboard(name, month, year) {
        const panel = await this.panelRepository.findOne({
            where: { name },
            relations: ['stats', 'pvsyst'],
        });
        if (!panel) {
            throw new common_1.NotFoundException('Panel not found');
        }
        if (!year || !month) {
            const stats = panel.stats;
            if (stats.length === 0) {
                throw new common_1.NotFoundException('No statistics found');
            }
            year = Math.max(...stats.map((stat) => stat.year));
            const statsForYear = stats.filter((stat) => stat.year === year);
            month = Math.max(...statsForYear.map((stat) => stat.month));
        }
        const highestMonth = Math.max(...panel.stats
            .filter((stat) => stat.year === year)
            .map((stat) => stat.month));
        const filteredStatsCurrentYear = panel.stats.filter((stat) => stat.year === year);
        const filteredStatsPreviousYear = panel.stats.filter((stat) => stat.year === year - 1);
        const filteredPvsystCurrentYear = panel.pvsyst.filter((pvsyst) => pvsyst.year === year);
        const filteredPvsystPreviousYear = panel.pvsyst.filter((pvsyst) => pvsyst.year === year - 1);
        const dia_a_dia = filteredStatsCurrentYear
            .filter((stat) => stat.month === month)
            .map((stat) => ({
            dia: stat.day,
            energiaGenerada: stat.energyGenerated,
        }));
        const energiaAcumuladaPorMes = {};
        filteredStatsCurrentYear.forEach((stat) => {
            if (!energiaAcumuladaPorMes[stat.month]) {
                energiaAcumuladaPorMes[stat.month] = {
                    energiaGeneradaAcumulada: 0,
                    pvsyst: 0,
                };
            }
            energiaAcumuladaPorMes[stat.month].energiaGeneradaAcumulada +=
                stat.energyGenerated;
        });
        filteredPvsystCurrentYear.forEach((pvsyst) => {
            if (!energiaAcumuladaPorMes[pvsyst.month]) {
                energiaAcumuladaPorMes[pvsyst.month] = {
                    energiaGeneradaAcumulada: 0,
                    pvsyst: 0,
                };
            }
            energiaAcumuladaPorMes[pvsyst.month].pvsyst = pvsyst.estimatedGeneration;
        });
        const mes_a_mes = Array.from({ length: highestMonth }, (_, i) => i + 1)
            .map((monthIndex) => ({
            mes: monthIndex,
            energiaGeneradaAcumulada: parseFloat((energiaAcumuladaPorMes[monthIndex]?.energiaGeneradaAcumulada || 0).toFixed(1)),
            pvsyst: energiaAcumuladaPorMes[monthIndex]?.pvsyst || 0,
        }))
            .filter((entry) => entry.energiaGeneradaAcumulada > 0 || entry.pvsyst > 0);
        let energiaGeneradaAnual = 0;
        let pvsystAnual = 0;
        filteredStatsCurrentYear
            .filter((stat) => stat.month <= highestMonth)
            .forEach((stat) => {
            energiaGeneradaAnual += stat.energyGenerated;
        });
        filteredPvsystCurrentYear
            .filter((pvsyst) => pvsyst.month <= highestMonth)
            .forEach((pvsyst) => {
            pvsystAnual += pvsyst.estimatedGeneration;
        });
        energiaGeneradaAnual = parseFloat(energiaGeneradaAnual.toFixed(1));
        pvsystAnual = parseFloat(pvsystAnual.toFixed(1));
        let energiaGeneradaAnualAnterior = 0;
        let pvsystAnualAnterior = 0;
        filteredStatsPreviousYear
            .filter((stat) => stat.month <= highestMonth)
            .forEach((stat) => {
            energiaGeneradaAnualAnterior += stat.energyGenerated;
        });
        filteredPvsystPreviousYear
            .filter((pvsyst) => pvsyst.month <= highestMonth)
            .forEach((pvsyst) => {
            pvsystAnualAnterior += pvsyst.estimatedGeneration;
        });
        energiaGeneradaAnualAnterior = parseFloat(energiaGeneradaAnualAnterior.toFixed(1));
        pvsystAnualAnterior = parseFloat(pvsystAnualAnterior.toFixed(1));
        let energiaGeneradaMesAnterior = 0;
        let pvsystMesAnterior = 0;
        filteredStatsPreviousYear
            .filter((stat) => stat.month === month)
            .forEach((stat) => {
            energiaGeneradaMesAnterior += stat.energyGenerated;
        });
        filteredPvsystPreviousYear
            .filter((pvsyst) => pvsyst.month === month)
            .forEach((pvsyst) => {
            pvsystMesAnterior += pvsyst.estimatedGeneration;
        });
        energiaGeneradaMesAnterior = parseFloat(energiaGeneradaMesAnterior.toFixed(1));
        pvsystMesAnterior = parseFloat(pvsystMesAnterior.toFixed(1));
        const dataMes = mes_a_mes.find((mes) => mes.mes === month);
        if (!dataMes) {
            return { respuesta: 'No data found for this month' };
        }
        const mesVsPvsystActual = parseFloat(((dataMes.energiaGeneradaAcumulada * 100) / dataMes.pvsyst).toFixed(1));
        const mesVsGeneradaAnterior = parseFloat(((dataMes.energiaGeneradaAcumulada * 100) /
            energiaGeneradaMesAnterior).toFixed(1));
        const añoVsPvsystActual = parseFloat(((energiaGeneradaAnual * 100) / pvsystAnual).toFixed(1));
        const añoVsGeneradaAnterior = parseFloat(((energiaGeneradaAnual * 100) / energiaGeneradaAnualAnterior).toFixed(1));
        const availableYears = await this.getAvailableYears(name);
        return {
            dia_a_dia,
            mes_a_mes,
            energíaMesActual: dataMes.energiaGeneradaAcumulada,
            mesVsPvsystActual,
            mesVsGeneradaAnterior,
            energíaAnualActual: energiaGeneradaAnual,
            añoVsPvsystActual,
            añoVsGeneradaAnterior,
            inversor: panel.inversor,
            address: panel.address,
            availableYears,
        };
    }
    async getAvailableYears(nameOfPanel) {
        const panel = await this.panelRepository.findOne({
            where: { name: nameOfPanel },
        });
        const availableYears = await this.availableYearsRepository.find({
            where: { panel: panel },
        });
        const arrayOfAvailableYears = availableYears.map((availableYear) => availableYear.year);
        return arrayOfAvailableYears;
    }
};
exports.DashboardService = DashboardService;
exports.DashboardService = DashboardService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectRepository)(panel_entity_1.Panel)),
    __param(1, (0, typeorm_2.InjectRepository)(stats_entity_1.Stats)),
    __param(2, (0, typeorm_2.InjectRepository)(pvsyst_entity_1.Pvsyst)),
    __param(3, (0, typeorm_2.InjectRepository)(availableYears_entity_1.AvailableYears)),
    __metadata("design:paramtypes", [typeorm_1.Repository,
        typeorm_1.Repository,
        typeorm_1.Repository,
        typeorm_1.Repository])
], DashboardService);
//# sourceMappingURL=getDataFromDashboard.js.map