"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PanelRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const panel_entity_1 = require("../entities/panel.entity");
const pvsystPreload_repository_1 = require("./pvsystPreload.repository");
const stats_entity_1 = require("../entities/stats.entity");
const getDataFromDashboard_1 = require("../helpers/getDataFromDashboard");
const preLoading_1 = require("../helpers/preLoading");
const availableYears_entity_1 = require("../entities/availableYears.entity");
const XLSX = require('xlsx');
let PanelRepository = class PanelRepository {
    constructor(pvsystPreloadRepository, Dashboard, preloading, panelRepository, statsRepository, availableYearsRepository) {
        this.pvsystPreloadRepository = pvsystPreloadRepository;
        this.Dashboard = Dashboard;
        this.preloading = preloading;
        this.panelRepository = panelRepository;
        this.statsRepository = statsRepository;
        this.availableYearsRepository = availableYearsRepository;
    }
    async onModuleInit() {
        await this.preloading.preloading();
    }
    async readExcel(buffer) {
        try {
            const workbook = XLSX.read(buffer, { type: 'buffer', raw: true });
            const sheet = workbook.Sheets[workbook.SheetNames[0]];
            const dataExcel = XLSX.utils.sheet_to_json(sheet);
            if (dataExcel.length === 0) {
                throw new common_1.BadRequestException('Excel is empty');
            }
            return dataExcel;
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw error;
        }
    }
    async extractDataIngecon(data) {
        try {
            if (data[0].GId) {
                const extractedData = [];
                for (const stat of data) {
                    let energy = stat['Energy(kWh)'];
                    if (stat['Energy(kWh)'] === 'NaN')
                        energy = '0';
                    extractedData.push({
                        day: stat['DateTime'].split(' ')[0].split('-')[2],
                        month: stat['DateTime'].split(' ')[0].split('-')[1],
                        year: stat['DateTime'].split(' ')[0].split('-')[0],
                        energyGenerated: energy,
                    });
                }
                return extractedData;
            }
            else {
                const extractedData = [];
                for (const stat of data) {
                    extractedData.push({
                        day: stat['dateTime'].split(' ')[0].split('-')[2],
                        month: stat['dateTime'].split(' ')[0].split('-')[1],
                        year: stat['dateTime'].split(' ')[0].split('-')[0],
                        energyGenerated: stat['pvGeneration(kWh)'],
                    });
                }
                return extractedData;
            }
        }
        catch (error) {
            throw error;
        }
    }
    async updatePanelStats(data, panelName) {
        try {
            const newData = await this.extractDataIngecon(data);
            const years = await this.availableYearsRepository.find({
                where: { year: newData[0].year },
            });
            if (years.length === 0) {
                console.log('i am here');
                const panel = await this.panelRepository.findOne({
                    where: { name: panelName },
                    relations: ['stats'],
                });
                if (!panel) {
                    throw new common_1.BadRequestException('Panel not found');
                }
                const newYear = this.availableYearsRepository.create({
                    year: newData[0].year,
                    panel: panel,
                });
                await this.availableYearsRepository.save(newYear);
            }
            const panel = await this.panelRepository.findOne({
                where: { name: panelName },
                relations: ['stats'],
            });
            if (!panel) {
                throw new common_1.BadRequestException('Panel not found');
            }
            const allStats = await this.statsRepository.find({
                where: { panel: { id: panel.id } },
                relations: ['panel'],
            });
            for (const item of newData) {
                const stat = this.statsRepository.create(item);
                let updated = false;
                for (const oldStat of allStats) {
                    if (stat.day == oldStat.day &&
                        stat.month == oldStat.month &&
                        stat.year == oldStat.year) {
                        await this.statsRepository.update(oldStat.id, {
                            energyGenerated: stat.energyGenerated,
                        });
                        updated = true;
                        break;
                    }
                }
                if (!updated) {
                    stat.panel = panel;
                    await this.statsRepository.save(stat);
                }
            }
            const updatedStats = await this.statsRepository.find({
                where: { panel: { id: panel.id } },
            });
            panel.stats = updatedStats;
            await this.panelRepository.save(panel);
            return newData;
        }
        catch (error) {
            throw error;
        }
    }
    async getAllPanels() {
        return await this.panelRepository.find();
    }
    async getPanelById(id) {
        const panel = await this.panelRepository.findOne({
            where: { id },
            relations: ['stats'],
        });
        if (panel && panel.stats) {
            panel.stats.sort((a, b) => {
                if (a.year !== b.year) {
                    return a.year - b.year;
                }
                if (a.month !== b.month) {
                    return a.month - b.month;
                }
                return a.day - b.day;
            });
        }
        return panel;
    }
    async getDataForDashboard(name, month, year) {
        return await this.Dashboard.dataForDashboard(name, month, year);
    }
    uploadPvsyst(data) {
        switch (data.panel) {
            case 'PLANT N1':
                return this.pvsystPreloadRepository.pvsystBodegasSalcobrand(data);
            case 'PLANT N2':
                return this.pvsystPreloadRepository.pvsystCentrovet(data);
            case 'PLANT N3':
                return this.pvsystPreloadRepository.pvsystCentrovet601(data);
            case 'PLANT N4':
                return this.pvsystPreloadRepository.pvsystEnokoElSalto(data);
            default:
                throw new Error('Panel not found');
        }
    }
};
exports.PanelRepository = PanelRepository;
exports.PanelRepository = PanelRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(3, (0, typeorm_1.InjectRepository)(panel_entity_1.Panel)),
    __param(4, (0, typeorm_1.InjectRepository)(stats_entity_1.Stats)),
    __param(5, (0, typeorm_1.InjectRepository)(availableYears_entity_1.AvailableYears)),
    __metadata("design:paramtypes", [pvsystPreload_repository_1.pvsystPreloadRepository,
        getDataFromDashboard_1.DashboardService,
        preLoading_1.Preloading,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], PanelRepository);
//# sourceMappingURL=panel.repository.js.map