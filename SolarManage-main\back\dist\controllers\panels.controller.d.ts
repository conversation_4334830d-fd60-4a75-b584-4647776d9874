import { Panel } from 'src/entities/panel.entity';
import { PanelRepository } from 'src/repositories/panel.repository';
export declare class PanelsController {
    private panelRepository;
    constructor(panelRepository: PanelRepository);
    uploadFile(file: Express.Multer.File, panelName: string): Promise<import("../dtos/stats.dto").StatsDto[] | {
        error: string;
    }>;
    getAllPanels(): Promise<Panel[]>;
    getDataForDashboard(data: any): Promise<{
        respuesta: string;
        dia_a_dia?: undefined;
        mes_a_mes?: undefined;
        energíaMesActual?: undefined;
        mesVsPvsystActual?: undefined;
        mesVsGeneradaAnterior?: undefined;
        energíaAnualActual?: undefined;
        añoVsPvsystActual?: undefined;
        añoVsGeneradaAnterior?: undefined;
        inversor?: undefined;
        address?: undefined;
        availableYears?: undefined;
    } | {
        dia_a_dia: {
            dia: number;
            energiaGenerada: number;
        }[];
        mes_a_mes: {
            mes: number;
            energiaGeneradaAcumulada: number;
            pvsyst: any;
        }[];
        energíaMesActual: number;
        mesVsPvsystActual: number;
        mesVsGeneradaAnterior: number;
        energíaAnualActual: number;
        añoVsPvsystActual: number;
        añoVsGeneradaAnterior: number;
        inversor: string;
        address: string;
        availableYears: number[];
        respuesta?: undefined;
    }>;
    getPanelById(id: string): Promise<Panel>;
    uploadPvsyst(data: any): Promise<void>;
}
