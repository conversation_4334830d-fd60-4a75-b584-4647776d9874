import { UserService } from 'src/services/user.service';
import { JwtService } from '@nestjs/jwt';
import { User } from 'src/entities/user.entity';
import { RegisterUserDto } from 'src/dtos/user.dto';
export declare class AuthRepository {
    private readonly userService;
    private readonly jwtService;
    constructor(userService: UserService, jwtService: JwtService);
    register(user: RegisterUserDto): Promise<Partial<User>>;
    createJwtToken(user: any): Promise<string>;
    login(email: string, password: string): Promise<{
        message: string;
        token: string;
    }>;
}
