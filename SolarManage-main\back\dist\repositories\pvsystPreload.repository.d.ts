import { Panel } from 'src/entities/panel.entity';
import { Pvsyst } from 'src/entities/pvsyst.entity';
import { Repository } from 'typeorm';
export declare class pvsystPreloadRepository {
    private readonly pvsystRepository;
    private readonly panelRepository;
    constructor(pvsystRepository: Repository<Pvsyst>, panelRepository: Repository<Panel>);
    pvsystBodegasSalcobrand(data?: any): Promise<void>;
    pvsystCentrovet(data?: any): Promise<void>;
    pvsystCentrovet601(data?: any): Promise<void>;
    pvsystEnokoElSalto(data?: any): Promise<void>;
}
