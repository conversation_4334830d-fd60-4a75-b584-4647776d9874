"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Preloading = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const panel_entity_1 = require("../entities/panel.entity");
const pvsystPreload_repository_1 = require("../repositories/pvsystPreload.repository");
const statsPreload_repository_1 = require("../repositories/statsPreload.repository");
const plantas_1 = require("../utils/plantas/plantas");
const bodegasSalcobrand_1 = require("../utils/bodegasSalcobrand/bodegasSalcobrand");
const centrovet_1 = require("../utils/centrovet255/centrovet");
const centrovet601_1 = require("../utils/centrovet601/centrovet601");
const eknoelsalto_1 = require("../utils/ekonoelsalto/eknoelsalto");
const availableYears_entity_1 = require("../entities/availableYears.entity");
const stats_entity_1 = require("../entities/stats.entity");
let Preloading = class Preloading {
    constructor(panelRepository, availableYearsRepository, statsRepository, pvsystPreloadRepository, statsPreloadRepository) {
        this.panelRepository = panelRepository;
        this.availableYearsRepository = availableYearsRepository;
        this.statsRepository = statsRepository;
        this.pvsystPreloadRepository = pvsystPreloadRepository;
        this.statsPreloadRepository = statsPreloadRepository;
    }
    async preloading() {
        for (const planta of plantas_1.plantas) {
            const panel = await this.panelRepository.findOne({
                where: { name: planta.name },
            });
            if (!panel) {
                const newPanel = this.panelRepository.create({
                    name: planta.name,
                    inversor: planta.inversor,
                    address: planta.address,
                });
                await this.panelRepository.save(newPanel);
                switch (newPanel.name) {
                    case 'PLANT N1':
                        await this.pvsystPreloadRepository.pvsystBodegasSalcobrand();
                        await this.statsPreloadRepository.saveStats('PLANT N1', bodegasSalcobrand_1.bodegasSalcobrand);
                        break;
                    case 'PLANT N2':
                        await this.pvsystPreloadRepository.pvsystCentrovet();
                        await this.statsPreloadRepository.saveStats('PLANT N2', centrovet_1.centrovet255);
                        break;
                    case 'PLANT N3':
                        await this.pvsystPreloadRepository.pvsystCentrovet601();
                        await this.statsPreloadRepository.saveStats('PLANT N3', centrovet601_1.centrovet601);
                        break;
                    case 'PLANT N4':
                        await this.pvsystPreloadRepository.pvsystEnokoElSalto();
                        await this.statsPreloadRepository.saveStats('PLANT N4', eknoelsalto_1.ekonoelsalto);
                        break;
                }
            }
        }
    }
};
exports.Preloading = Preloading;
exports.Preloading = Preloading = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(panel_entity_1.Panel)),
    __param(1, (0, typeorm_1.InjectRepository)(availableYears_entity_1.AvailableYears)),
    __param(2, (0, typeorm_1.InjectRepository)(stats_entity_1.Stats)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        pvsystPreload_repository_1.pvsystPreloadRepository,
        statsPreload_repository_1.statsPreloadRepository])
], Preloading);
//# sourceMappingURL=preLoading.js.map