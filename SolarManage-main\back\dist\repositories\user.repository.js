"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_entity_1 = require("../entities/user.entity");
const role_enum_1 = require("../enum/role.enum");
const typeorm_2 = require("typeorm");
const bcrypt = require("bcrypt");
(0, common_1.Injectable)();
let UserRepository = class UserRepository {
    constructor(userRepository) {
        this.userRepository = userRepository;
    }
    async onModuleInit() {
        const user = await this.userRepository.findOne({
            where: { email: '<EMAIL>' },
        });
        if (!user) {
            const hashedPassword = await bcrypt.hash('Password1!', 10);
            const newUser = this.userRepository.create({
                name: 'Admin',
                email: '<EMAIL>',
                password: hashedPassword,
                phone: '123456789',
                role: role_enum_1.Role.Admin,
            });
            await this.userRepository.save(newUser);
        }
    }
    async getAllUsers(page, limit) {
        const [users] = await this.userRepository.findAndCount({
            skip: (page - 1) * limit,
            take: limit,
        });
        if (!users.length) {
            throw new common_1.NotFoundException('Users not found, please create at least one');
        }
        return users;
    }
    async getUserById(id) {
        const user = await this.userRepository.findOne({ where: { id } });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async getUserByEmail(email) {
        const user = await this.userRepository.findOne({ where: { email } });
        return user;
    }
    async updateUser(id, data) {
        const user = await this.userRepository.findOne({ where: { id } });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (data.password) {
            const hashedPassword = await bcrypt.hash(data.password, 10);
            data.password = hashedPassword;
        }
        const updatedUser = this.userRepository.merge(user, data);
        await this.userRepository.save(updatedUser);
        return updatedUser;
    }
    async delete(id) {
        const user = await this.userRepository.findOneBy({ id });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.userRepository.delete(id);
        return 'User deleted';
    }
    async createUser(user) {
        const userExist = await this.userRepository.findOne({
            where: { email: user.email },
        });
        if (userExist) {
            throw new common_1.BadRequestException('User with this email already exists');
        }
        const hashedPassword = await bcrypt.hash(user.password, 10);
        const newUser = this.userRepository.create({
            ...user,
            password: hashedPassword,
        });
        await this.userRepository.save(newUser);
        const { id, password, ...rest } = newUser;
        return rest;
    }
};
exports.UserRepository = UserRepository;
exports.UserRepository = UserRepository = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UserRepository);
//# sourceMappingURL=user.repository.js.map