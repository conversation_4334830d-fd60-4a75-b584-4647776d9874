import { UserDto } from 'src/dtos/updateuser.dtos';
import { User } from 'src/entities/user.entity';
import { UserService } from 'src/services/user.service';
export declare class UsersController {
    private userService;
    constructor(userService: UserService);
    getAllUsers(page?: number, limit?: number): Promise<User[]>;
    getUserById(id: string): Promise<User>;
    updateUser(id: string, data: Partial<UserDto>): Promise<User>;
    deleteUser(id: string): Promise<string>;
}
