"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.pvsystPreloadRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const panel_entity_1 = require("../entities/panel.entity");
const pvsyst_entity_1 = require("../entities/pvsyst.entity");
const pvsyst_1 = require("../utils/pvsyst");
const typeorm_2 = require("typeorm");
let pvsystPreloadRepository = class pvsystPreloadRepository {
    constructor(pvsystRepository, panelRepository) {
        this.pvsystRepository = pvsystRepository;
        this.panelRepository = panelRepository;
    }
    async pvsystBodegasSalcobrand(data) {
        if (data) {
            const panel = await this.panelRepository.findOne({
                where: { name: data.panel },
            });
            if (!panel) {
                throw new Error('Panel not found');
            }
            const newPvsyst = this.pvsystRepository.create({
                month: data.month,
                year: data.year,
                estimatedGeneration: data.estimatedGeneration,
                panel: panel,
            });
            await this.pvsystRepository.save(newPvsyst);
        }
        else {
            const panel = await this.panelRepository.findOne({
                where: { name: 'PLANT N1' },
            });
            if (!panel) {
                throw new Error('Panel not found');
            }
            for (const pvsyst of pvsyst_1.pvsystBS) {
                const newPvsyst = this.pvsystRepository.create({
                    month: pvsyst.month,
                    year: pvsyst.year,
                    estimatedGeneration: pvsyst.estimatedGeneration,
                    panel: panel,
                });
                await this.pvsystRepository.save(newPvsyst);
            }
        }
    }
    async pvsystCentrovet(data) {
        if (data) {
            console.log('hola');
            const panel = await this.panelRepository.findOne({
                where: { name: data.panel },
            });
            if (!panel) {
                throw new Error('Panel not found');
            }
            const newPvsyst = this.pvsystRepository.create({
                month: data.month,
                year: data.year,
                estimatedGeneration: data.estimatedGeneration,
                panel: panel,
            });
            await this.pvsystRepository.save(newPvsyst);
        }
        else {
            const panel = await this.panelRepository.findOne({
                where: { name: 'PLANT N2' },
            });
            if (!panel) {
                throw new Error('Panel not found');
            }
            for (const pvsyst of pvsyst_1.pvsystCentrovet) {
                const newPvsyst = this.pvsystRepository.create({
                    month: pvsyst.month,
                    year: pvsyst.year,
                    estimatedGeneration: pvsyst.estimatedGeneration,
                    panel: panel,
                });
                await this.pvsystRepository.save(newPvsyst);
            }
        }
    }
    async pvsystCentrovet601(data) {
        if (data) {
            const panel = await this.panelRepository.findOne({
                where: { name: data.panel },
            });
            if (!panel) {
                throw new Error('Panel not found');
            }
            const newPvsyst = this.pvsystRepository.create({
                month: data.month,
                year: data.year,
                estimatedGeneration: data.estimatedGeneration,
                panel: panel,
            });
            await this.pvsystRepository.save(newPvsyst);
        }
        else {
            const panel = await this.panelRepository.findOne({
                where: { name: 'PLANT N3' },
            });
            if (!panel) {
                throw new Error('Panel not found');
            }
            for (const pvsyst of pvsyst_1.pvsystCentrovet601) {
                const newPvsyst = this.pvsystRepository.create({
                    month: pvsyst.month,
                    year: pvsyst.year,
                    estimatedGeneration: pvsyst.estimatedGeneration,
                    panel: panel,
                });
                await this.pvsystRepository.save(newPvsyst);
            }
        }
    }
    async pvsystEnokoElSalto(data) {
        if (data) {
            const panel = await this.panelRepository.findOne({
                where: { name: data.panel },
            });
            if (!panel) {
                throw new Error('Panel not found');
            }
            const newPvsyst = this.pvsystRepository.create({
                month: data.month,
                year: data.year,
                estimatedGeneration: data.estimatedGeneration,
                panel: panel,
            });
            await this.pvsystRepository.save(newPvsyst);
        }
        else {
            const panel = await this.panelRepository.findOne({
                where: { name: 'PLANT N4' },
            });
            if (!panel) {
                throw new Error('Panel not found');
            }
            for (const pvsyst of pvsyst_1.pvsystEnokoElSalto) {
                const newPvsyst = this.pvsystRepository.create({
                    month: pvsyst.month,
                    year: pvsyst.year,
                    estimatedGeneration: pvsyst.estimatedGeneration,
                    panel: panel,
                });
                await this.pvsystRepository.save(newPvsyst);
            }
        }
    }
};
exports.pvsystPreloadRepository = pvsystPreloadRepository;
exports.pvsystPreloadRepository = pvsystPreloadRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(pvsyst_entity_1.Pvsyst)),
    __param(1, (0, typeorm_1.InjectRepository)(panel_entity_1.Panel)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], pvsystPreloadRepository);
//# sourceMappingURL=pvsystPreload.repository.js.map