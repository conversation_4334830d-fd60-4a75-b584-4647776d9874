"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PanelsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const stats_entity_1 = require("../entities/stats.entity");
const panels_controller_1 = require("../controllers/panels.controller");
const panel_entity_1 = require("../entities/panel.entity");
const pvsyst_entity_1 = require("../entities/pvsyst.entity");
const panel_repository_1 = require("../repositories/panel.repository");
const pvsystPreload_repository_1 = require("../repositories/pvsystPreload.repository");
const statsPreload_repository_1 = require("../repositories/statsPreload.repository");
const getDataFromDashboard_1 = require("../helpers/getDataFromDashboard");
const preLoading_1 = require("../helpers/preLoading");
const availableYears_entity_1 = require("../entities/availableYears.entity");
let PanelsModule = class PanelsModule {
};
exports.PanelsModule = PanelsModule;
exports.PanelsModule = PanelsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([panel_entity_1.Panel, stats_entity_1.Stats, pvsyst_entity_1.Pvsyst, availableYears_entity_1.AvailableYears])],
        controllers: [panels_controller_1.PanelsController],
        providers: [
            panel_repository_1.PanelRepository,
            pvsystPreload_repository_1.pvsystPreloadRepository,
            statsPreload_repository_1.statsPreloadRepository,
            getDataFromDashboard_1.DashboardService,
            preLoading_1.Preloading,
        ],
        exports: [],
    })
], PanelsModule);
//# sourceMappingURL=panels.module.js.map