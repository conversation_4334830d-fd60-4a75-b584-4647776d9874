"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Panel = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const stats_entity_1 = require("./stats.entity");
const pvsyst_entity_1 = require("./pvsyst.entity");
const availableYears_entity_1 = require("./availableYears.entity");
let Panel = class Panel {
    constructor() {
        this.id = (0, uuid_1.v4)();
    }
};
exports.Panel = Panel;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Panel.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Panel.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Panel.prototype, "inversor", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Panel.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => stats_entity_1.Stats, (stat) => stat.panel),
    (0, typeorm_1.JoinColumn)(),
    __metadata("design:type", Array)
], Panel.prototype, "stats", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => pvsyst_entity_1.Pvsyst, (pvsyst) => pvsyst.panel),
    (0, typeorm_1.JoinColumn)(),
    __metadata("design:type", Array)
], Panel.prototype, "pvsyst", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => availableYears_entity_1.AvailableYears, (availableYears) => availableYears.panel),
    __metadata("design:type", Array)
], Panel.prototype, "availableYears", void 0);
exports.Panel = Panel = __decorate([
    (0, typeorm_1.Entity)({ name: 'panel' })
], Panel);
//# sourceMappingURL=panel.entity.js.map