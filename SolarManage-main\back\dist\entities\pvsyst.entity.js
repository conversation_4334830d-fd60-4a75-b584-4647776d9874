"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Pvsyst = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const panel_entity_1 = require("./panel.entity");
let Pvsyst = class Pvsyst {
    constructor() {
        this.id = (0, uuid_1.v4)();
    }
};
exports.Pvsyst = Pvsyst;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Pvsyst.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], Pvsyst.prototype, "month", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], Pvsyst.prototype, "year", void 0);
__decorate([
    (0, typeorm_1.Column)('float'),
    __metadata("design:type", Number)
], Pvsyst.prototype, "estimatedGeneration", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => panel_entity_1.Panel, (panel) => panel.pvsyst),
    __metadata("design:type", panel_entity_1.Panel)
], Pvsyst.prototype, "panel", void 0);
exports.Pvsyst = Pvsyst = __decorate([
    (0, typeorm_1.Entity)({ name: 'pvsyst' })
], Pvsyst);
//# sourceMappingURL=pvsyst.entity.js.map