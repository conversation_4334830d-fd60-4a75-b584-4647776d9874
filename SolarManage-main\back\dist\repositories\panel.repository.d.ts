import { OnModuleInit } from '@nestjs/common';
import { StatsDto } from 'src/dtos/stats.dto';
import { Repository } from 'typeorm';
import { Panel } from 'src/entities/panel.entity';
import { pvsystPreloadRepository } from './pvsystPreload.repository';
import { Stats } from 'src/entities/stats.entity';
import { DashboardService } from 'src/helpers/getDataFromDashboard';
import { Preloading } from 'src/helpers/preLoading';
import { AvailableYears } from 'src/entities/availableYears.entity';
export declare class PanelRepository implements OnModuleInit {
    private readonly pvsystPreloadRepository;
    private readonly Dashboard;
    private readonly preloading;
    private readonly panelRepository;
    private readonly statsRepository;
    private readonly availableYearsRepository;
    constructor(pvsystPreloadRepository: pvsystPreloadRepository, Dashboard: DashboardService, preloading: Preloading, panelRepository: Repository<Panel>, statsRepository: Repository<Stats>, availableYearsRepository: Repository<AvailableYears>);
    onModuleInit(): Promise<void>;
    readExcel(buffer: Buffer): Promise<string>;
    extractDataIngecon(data: any): Promise<StatsDto[]>;
    updatePanelStats(data: any, panelName: string): Promise<StatsDto[]>;
    getAllPanels(): Promise<Panel[]>;
    getPanelById(id: string): Promise<Panel>;
    getDataForDashboard(name: string, month?: number, year?: number): Promise<{
        respuesta: string;
        dia_a_dia?: undefined;
        mes_a_mes?: undefined;
        energíaMesActual?: undefined;
        mesVsPvsystActual?: undefined;
        mesVsGeneradaAnterior?: undefined;
        energíaAnualActual?: undefined;
        añoVsPvsystActual?: undefined;
        añoVsGeneradaAnterior?: undefined;
        inversor?: undefined;
        address?: undefined;
        availableYears?: undefined;
    } | {
        dia_a_dia: {
            dia: number;
            energiaGenerada: number;
        }[];
        mes_a_mes: {
            mes: number;
            energiaGeneradaAcumulada: number;
            pvsyst: any;
        }[];
        energíaMesActual: number;
        mesVsPvsystActual: number;
        mesVsGeneradaAnterior: number;
        energíaAnualActual: number;
        añoVsPvsystActual: number;
        añoVsGeneradaAnterior: number;
        inversor: string;
        address: string;
        availableYears: number[];
        respuesta?: undefined;
    }>;
    uploadPvsyst(data: any): Promise<void>;
}
