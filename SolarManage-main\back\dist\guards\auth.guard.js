"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthGUard = void 0;
const common_1 = require("@nestjs/common");
const dotenv_1 = require("dotenv");
const jwt_1 = require("@nestjs/jwt");
(0, dotenv_1.config)({ path: '.env' });
let AuthGUard = class AuthGUard {
    constructor(jwtService) {
        this.jwtService = jwtService;
    }
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const token = request.headers.authorization?.split(' ')[1];
        if (!token) {
            throw new common_1.ForbiddenException('Token not found');
        }
        try {
            const secret = process.env.JWT_SECRET;
            const payload = this.jwtService.verify(token, { secret });
            request.user = payload;
            return payload;
        }
        catch (error) {
            throw new common_1.ForbiddenException('Invalid token');
        }
    }
};
exports.AuthGUard = AuthGUard;
exports.AuthGUard = AuthGUard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService])
], AuthGUard);
//# sourceMappingURL=auth.guard.js.map