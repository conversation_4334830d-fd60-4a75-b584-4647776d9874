"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.statsPreloadRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const panel_entity_1 = require("../entities/panel.entity");
const stats_entity_1 = require("../entities/stats.entity");
const typeorm_2 = require("typeorm");
const availableYears_entity_1 = require("../entities/availableYears.entity");
let statsPreloadRepository = class statsPreloadRepository {
    constructor(statsRepository, panelRepository, availableYearsRepository) {
        this.statsRepository = statsRepository;
        this.panelRepository = panelRepository;
        this.availableYearsRepository = availableYearsRepository;
    }
    async saveStats(plantName, statsData) {
        const panel = await this.panelRepository.findOne({
            where: { name: plantName },
        });
        if (!panel) {
            throw new Error(`Panel with name ${plantName} not found`);
        }
        for (const stats of statsData) {
            const newStats = this.statsRepository.create({
                day: stats.day,
                month: stats.month,
                year: stats.year,
                energyGenerated: stats.energyGenerated,
                panel: panel,
            });
            await this.statsRepository.save(newStats);
        }
        for (let i = 2023; i <= 2024; i++) {
            const data = await this.statsRepository.findOne({
                where: { panel: panel },
            });
            const newYear = this.availableYearsRepository.create({
                year: i,
                panel: panel,
            });
            await this.availableYearsRepository.save(newYear);
        }
    }
};
exports.statsPreloadRepository = statsPreloadRepository;
exports.statsPreloadRepository = statsPreloadRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(stats_entity_1.Stats)),
    __param(1, (0, typeorm_1.InjectRepository)(panel_entity_1.Panel)),
    __param(2, (0, typeorm_1.InjectRepository)(availableYears_entity_1.AvailableYears)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], statsPreloadRepository);
//# sourceMappingURL=statsPreload.repository.js.map