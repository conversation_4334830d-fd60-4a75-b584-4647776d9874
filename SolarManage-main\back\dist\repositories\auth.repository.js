"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthRepository = void 0;
const common_1 = require("@nestjs/common");
const user_service_1 = require("../services/user.service");
const bcrypt = require("bcrypt");
const jwt_1 = require("@nestjs/jwt");
let AuthRepository = class AuthRepository {
    constructor(userService, jwtService) {
        this.userService = userService;
        this.jwtService = jwtService;
    }
    async register(user) {
        const newUser = this.userService.createUser(user);
        if (!newUser) {
            throw new common_1.NotFoundException('User not created');
        }
        return newUser;
    }
    async createJwtToken(user) {
        const payload = {
            id: user.id,
            email: user.email,
            role: user.role,
        };
        return this.jwtService.sign(payload, { secret: process.env.JWT_SECRET });
    }
    async login(email, password) {
        const user = await this.userService.getUserByEmail(email);
        if (!user) {
            throw new common_1.NotFoundException('invalid email');
        }
        const validatePassword = await bcrypt.compare(password, user.password);
        if (!validatePassword) {
            throw new common_1.NotFoundException('invalid email or password');
        }
        const token = await this.createJwtToken(user);
        return {
            message: 'Login successful',
            token,
        };
    }
};
exports.AuthRepository = AuthRepository;
exports.AuthRepository = AuthRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [user_service_1.UserService,
        jwt_1.JwtService])
], AuthRepository);
//# sourceMappingURL=auth.repository.js.map