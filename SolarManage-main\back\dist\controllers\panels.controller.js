"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PanelsController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const panel_repository_1 = require("../repositories/panel.repository");
let PanelsController = class PanelsController {
    constructor(panelRepository) {
        this.panelRepository = panelRepository;
    }
    async uploadFile(file, panelName) {
        if (!file) {
            throw new Error('No file uploaded');
        }
        try {
            const data = await this.panelRepository.readExcel(file.buffer);
            return await this.panelRepository.updatePanelStats(data, panelName);
        }
        catch (error) {
            console.error(error);
            return { error: `Failed to process file: ${error.message}` };
        }
    }
    async getAllPanels() {
        return await this.panelRepository.getAllPanels();
    }
    async getDataForDashboard(data) {
        const { name, month, year } = data;
        if (!name) {
            throw new common_1.BadRequestException('Missing name');
        }
        return await this.panelRepository.getDataForDashboard(name, month, year);
    }
    async getPanelById(id) {
        return await this.panelRepository.getPanelById(id);
    }
    async uploadPvsyst(data) {
        return await this.panelRepository.uploadPvsyst(data);
    }
};
exports.PanelsController = PanelsController;
__decorate([
    (0, common_1.Post)('upload'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)('panelName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PanelsController.prototype, "uploadFile", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PanelsController.prototype, "getAllPanels", null);
__decorate([
    (0, common_1.Post)('stats'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PanelsController.prototype, "getDataForDashboard", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PanelsController.prototype, "getPanelById", null);
__decorate([
    (0, common_1.Post)('uploadPvsyst'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PanelsController.prototype, "uploadPvsyst", null);
exports.PanelsController = PanelsController = __decorate([
    (0, common_1.Controller)('panels'),
    __metadata("design:paramtypes", [panel_repository_1.PanelRepository])
], PanelsController);
//# sourceMappingURL=panels.controller.js.map