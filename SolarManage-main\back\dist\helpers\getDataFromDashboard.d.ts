import { Repository } from 'typeorm';
import { Panel } from 'src/entities/panel.entity';
import { Stats } from 'src/entities/stats.entity';
import { Pvsyst } from 'src/entities/pvsyst.entity';
import { AvailableYears } from 'src/entities/availableYears.entity';
export declare class DashboardService {
    private readonly panelRepository;
    private readonly statsRepository;
    private readonly pvsystRepository;
    private readonly availableYearsRepository;
    constructor(panelRepository: Repository<Panel>, statsRepository: Repository<Stats>, pvsystRepository: Repository<Pvsyst>, availableYearsRepository: Repository<AvailableYears>);
    dataForDashboard(name: string, month?: number, year?: number): Promise<{
        respuesta: string;
        dia_a_dia?: undefined;
        mes_a_mes?: undefined;
        energíaMesActual?: undefined;
        mesVsPvsystActual?: undefined;
        mesVsGeneradaAnterior?: undefined;
        energíaAnualActual?: undefined;
        añoVsPvsystActual?: undefined;
        añoVsGeneradaAnterior?: undefined;
        inversor?: undefined;
        address?: undefined;
        availableYears?: undefined;
    } | {
        dia_a_dia: {
            dia: number;
            energiaGenerada: number;
        }[];
        mes_a_mes: {
            mes: number;
            energiaGeneradaAcumulada: number;
            pvsyst: any;
        }[];
        energíaMesActual: number;
        mesVsPvsystActual: number;
        mesVsGeneradaAnterior: number;
        energíaAnualActual: number;
        añoVsPvsystActual: number;
        añoVsGeneradaAnterior: number;
        inversor: string;
        address: string;
        availableYears: number[];
        respuesta?: undefined;
    }>;
    getAvailableYears(nameOfPanel: string): Promise<number[]>;
}
