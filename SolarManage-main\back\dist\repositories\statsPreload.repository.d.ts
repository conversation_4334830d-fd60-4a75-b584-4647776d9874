import { StatsDto } from 'src/dtos/stats.dto';
import { Panel } from 'src/entities/panel.entity';
import { Stats } from 'src/entities/stats.entity';
import { Repository } from 'typeorm';
import { AvailableYears } from 'src/entities/availableYears.entity';
export declare class statsPreloadRepository {
    private readonly statsRepository;
    private readonly panelRepository;
    private readonly availableYearsRepository;
    constructor(statsRepository: Repository<Stats>, panelRepository: Repository<Panel>, availableYearsRepository: Repository<AvailableYears>);
    saveStats(plantName: string, statsData: StatsDto[]): Promise<void>;
}
